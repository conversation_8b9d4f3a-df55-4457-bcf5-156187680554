<template>
  <!-- 轮播 -->
  <div class="swiper-xxxx">
    <GoodsSwiper
      v-if="imageList && imageList.length > 0"
      :image-list="imageList"
      :autoplay="true"
      :autoplay-delay="3000"
      :loop="true"
      :show-pagination="true"
      mode="landscape"
      @image-click="onImageClick"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import GoodsSwiper from '@/components/Common/GoodsSwiper.vue'
import { getBannerInfo } from '@/api/interface/bannerIcon'
import { getBizCode } from '@/utils/curEnv'
import { closeToast, showLoadingToast, showToast } from 'vant'

// 响应式数据
const imageList = ref([])

// 初始化函数
const init = async () => {
  const params = {
    showPage: '1',
    bizCode: getBizCode('GOODS')
  }
  showLoadingToast()
  const [err, json] = await getBannerInfo(params)
  closeToast()
  if (err) {
    showToast(err.msg)
    return
  }

  // 转换数据格式为 GoodsSwiper 期望的格式
  imageList.value = json.map((item, index) => ({
    id: item.id || index,
    url: item.imgUrl,
    alt: item.bannerChName || `轮播图 ${index + 1}`,
    title: item.bannerChName,
    linkUrl: item.url
  }))
}

// 图片点击事件处理
const onImageClick = ({ item }) => {
  if (item.linkUrl) {
    window.location.href = item.linkUrl
  }
}

// 生命周期
onMounted(() => {
  init()
})
</script>

<style lang='less' scoped>
.swiper {
  padding: 0 17px;
}
</style>
